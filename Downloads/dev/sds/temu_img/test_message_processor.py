#!/usr/bin/env python3
"""
消息处理器测试
"""
import json
import unittest
from unittest.mock import Mock, patch, MagicMock
from dotenv import load_dotenv

from models import SQSMessage, ImgOption, TemuProductRecord
from message_processor import MessageProcessor
from logger_config import configure_logging


class TestMessageProcessor(unittest.TestCase):
    """消息处理器测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        load_dotenv()
        configure_logging()
    
    def setUp(self):
        """每个测试方法的初始化"""
        # Mock所有外部依赖
        with patch('message_processor.SQSHandler'), \
             patch('message_processor.DynamoDBHandler'), \
             patch('message_processor.WebhookHandler'):
            self.processor = MessageProcessor()
    
    def test_parse_sqs_message(self):
        """测试SQS消息解析"""
        # 准备测试数据
        test_message_body = {
            "webhook": "https://example.com/webhook",
            "env": "test",
            "seller_id": "seller123",
            "merchantStoreRegion": "US",
            "imgOption": {
                "temuProductType": "local",
                "imgUrl": "https://example.com/image.jpg",
                "temuImgBizType": 1,
                "temuImgSizeMode": 1,
                "temuCateId": "123"
            }
        }
        
        raw_message = {
            "Body": json.dumps(test_message_body),
            "ReceiptHandle": "test-receipt-handle",
            "MessageId": "test-message-id"
        }
        
        # Mock SQS handler的parse_message方法
        expected_message = SQSMessage(**test_message_body)
        self.processor.sqs_handler.parse_message.return_value = expected_message
        
        # 执行测试
        result = self.processor.sqs_handler.parse_message(raw_message)
        
        # 验证结果
        self.assertIsInstance(result, SQSMessage)
        self.assertEqual(result.seller_id, "seller123")
        self.assertEqual(result.env, "test")
        self.assertEqual(result.imgOption.temuProductType, "local")
    
    def test_successful_message_processing(self):
        """测试成功的消息处理流程"""
        # 准备测试数据
        test_message_body = {
            "webhook": "https://example.com/webhook",
            "env": "test",
            "seller_id": "seller123",
            "merchantStoreRegion": "US",
            "imgOption": {
                "temuProductType": "local",
                "imgUrl": "https://example.com/image.jpg",
                "temuImgBizType": 1,
                "temuImgSizeMode": 1,
                "temuCateId": "123"
            }
        }
        
        raw_message = {
            "Body": json.dumps(test_message_body),
            "ReceiptHandle": "test-receipt-handle",
            "MessageId": "test-message-id"
        }
        
        # Mock各个组件的返回值
        parsed_message = SQSMessage(**test_message_body)
        product_record = TemuProductRecord(
            seller_id="seller123",
            env="test",
            token="test-token-123"
        )
        
        self.processor.sqs_handler.parse_message.return_value = parsed_message
        self.processor.dynamodb_handler.get_product_record.return_value = product_record
        self.processor.webhook_handler.send_webhook_notification.return_value = Mock(success=True)
        self.processor.sqs_handler.delete_message.return_value = True
        
        # 执行测试
        result = self.processor.process_single_message(raw_message)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证各个方法被正确调用
        self.processor.sqs_handler.parse_message.assert_called_once_with(raw_message)
        self.processor.dynamodb_handler.get_product_record.assert_called_once_with("seller123", "test")
        self.processor.webhook_handler.send_webhook_notification.assert_called_once()
        self.processor.sqs_handler.delete_message.assert_called_once_with("test-receipt-handle")
    
    def test_message_processing_with_missing_seller(self):
        """测试商户不存在的情况"""
        test_message_body = {
            "webhook": "https://example.com/webhook",
            "env": "test",
            "seller_id": "nonexistent_seller",
            "merchantStoreRegion": "US",
            "imgOption": {
                "temuProductType": "local",
                "imgUrl": "https://example.com/image.jpg",
                "temuImgBizType": 1,
                "temuImgSizeMode": 1,
                "temuCateId": "123"
            }
        }
        
        raw_message = {
            "Body": json.dumps(test_message_body),
            "ReceiptHandle": "test-receipt-handle",
            "MessageId": "test-message-id"
        }
        
        # Mock返回值
        parsed_message = SQSMessage(**test_message_body)
        self.processor.sqs_handler.parse_message.return_value = parsed_message
        self.processor.dynamodb_handler.get_product_record.return_value = None  # 商户不存在
        self.processor.webhook_handler.send_webhook_notification.return_value = Mock(success=True)
        
        # 执行测试
        result = self.processor.process_single_message(raw_message)
        
        # 验证结果
        self.assertFalse(result)
        
        # 验证错误通知被发送
        self.processor.webhook_handler.send_webhook_notification.assert_called_once()
        call_args = self.processor.webhook_handler.send_webhook_notification.call_args
        self.assertFalse(call_args.kwargs['success'])
    
    def test_single_message_from_queue_processing(self):
        """测试从队列处理单个消息"""
        # 准备测试数据
        test_message = {
            "Body": json.dumps({
                "webhook": "https://example.com/webhook1",
                "env": "test",
                "seller_id": "seller1",
                "merchantStoreRegion": "US",
                "imgOption": {
                    "temuProductType": "local",
                    "imgUrl": "https://example.com/image1.jpg",
                    "temuImgBizType": 1,
                    "temuImgSizeMode": 1,
                    "temuCateId": "123"
                }
            }),
            "ReceiptHandle": "handle1",
            "MessageId": "msg1"
        }

        # Mock SQS接收单个消息
        self.processor.sqs_handler.receive_messages.return_value = [test_message]

        # Mock process_single_message方法
        with patch.object(self.processor, 'process_single_message') as mock_process:
            mock_process.return_value = True  # 消息处理成功

            # 执行测试
            success_count, total_count = self.processor.process_single_message_from_queue()

            # 验证结果
            self.assertEqual(success_count, 1)
            self.assertEqual(total_count, 1)
            self.assertEqual(mock_process.call_count, 1)

            # 验证调用参数
            mock_process.assert_called_once_with(test_message)

    def test_single_message_from_queue_no_messages(self):
        """测试队列中没有消息的情况"""
        # Mock SQS没有消息
        self.processor.sqs_handler.receive_messages.return_value = []

        # 执行测试
        success_count, total_count = self.processor.process_single_message_from_queue()

        # 验证结果
        self.assertEqual(success_count, 0)
        self.assertEqual(total_count, 0)

    def test_batch_processing(self):
        """测试批量处理（保留兼容性测试）"""
        # 准备测试数据
        test_messages = [
            {
                "Body": json.dumps({
                    "webhook": "https://example.com/webhook1",
                    "env": "test",
                    "seller_id": "seller1",
                    "merchantStoreRegion": "US",
                    "imgOption": {
                        "temuProductType": "local",
                        "imgUrl": "https://example.com/image1.jpg",
                        "temuImgBizType": 1,
                        "temuImgSizeMode": 1,
                        "temuCateId": "123"
                    }
                }),
                "ReceiptHandle": "handle1",
                "MessageId": "msg1"
            },
            {
                "Body": json.dumps({
                    "webhook": "https://example.com/webhook2",
                    "env": "test",
                    "seller_id": "seller2",
                    "merchantStoreRegion": "US",
                    "imgOption": {
                        "temuProductType": "temu_semi",
                        "imgUrl": "https://example.com/image2.jpg",
                        "temuImgBizType": 2,
                        "temuImgSizeMode": 2,
                        "temuCateId": "456"
                    }
                }),
                "ReceiptHandle": "handle2",
                "MessageId": "msg2"
            }
        ]

        # Mock SQS接收消息
        self.processor.sqs_handler.receive_messages.return_value = test_messages

        # Mock process_single_message方法
        with patch.object(self.processor, 'process_single_message') as mock_process:
            mock_process.side_effect = [True, True]  # 两个消息都处理成功

            # 执行测试
            success_count, total_count = self.processor.process_messages_batch(10)

            # 验证结果
            self.assertEqual(success_count, 2)
            self.assertEqual(total_count, 2)
            self.assertEqual(mock_process.call_count, 2)


def run_tests():
    """运行测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()
