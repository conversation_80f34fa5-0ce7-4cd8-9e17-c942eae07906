#!/usr/bin/env python3
"""
测试单个SQS消息处理
"""
import json
import boto3
from unittest.mock import MagicMock
from dotenv import load_dotenv

# 导入主程序模块
from main import process_one_sqs_message, setup_logging, logger

def test_single_message():
    """测试单个消息处理"""
    # 加载环境变量和设置日志
    load_dotenv()
    setup_logging()
    
    # 创建模拟的DynamoDB客户端
    mock_dynamodb = MagicMock()
    
    # 测试消息1：temuProductType=local
    test_message_local = {
        "webhook": "https://webhook.example.com/callback",
        "env": "test",
        "seller_id": "test_seller_123",
        "merchantStoreRegion": "US",
        "temuProductType": "local",
        "imgOption": {
            "imgUrl": "https://example.com/test-image.jpg",
            "temuImgBizType": 1,
            "temuImgSizeMode": 2,
            "temuCateId": "123"
        }
    }
    
    # 测试消息2：temuProductType=temu_semi
    test_message_semi = {
        "webhook": "https://webhook.example.com/callback",
        "env": "test",
        "seller_id": "test_seller_456",
        "merchantStoreRegion": "US",
        "temuProductType": "temu_semi",
        "imgOption": {
            "imgUrl": "https://cdn.sdspod.com/test-image.jpg",
            "temuImgBizType": 2,
            "temuImgSizeMode": 1,
            "temuCateId": "456"
        }
    }
    
    print("=== 测试消息处理 ===")
    
    # 选择要测试的消息
    test_message = test_message_local  # 或者 test_message_semi
    
    try:
        print(f"测试消息: {json.dumps(test_message, indent=2, ensure_ascii=False)}")
        print("\n开始处理...")
        
        # 调用业务处理方法
        result = process_one_sqs_message(mock_dynamodb, test_message)
        
        print(f"\n处理成功!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"\n处理失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")


def test_with_mock_data():
    """使用模拟数据测试"""
    # 加载环境变量和设置日志
    load_dotenv()
    setup_logging()
    
    # 创建模拟的DynamoDB客户端
    mock_dynamodb = MagicMock()
    
    # 模拟DynamoDB返回数据
    mock_table = MagicMock()
    mock_dynamodb.Table.return_value = mock_table
    
    # 模拟成功的商户数据
    mock_table.get_item.return_value = {
        'Item': {
            'accessToken': 'test_access_token_123',
            'appKey': 'test_app_key_123',
            'appSecret': 'test_app_secret_123',
            'authStatus': 'SUCCESS'
        }
    }
    
    # 测试消息
    test_message = {
        "webhook": "https://webhook.example.com/callback",
        "env": "test",
        "seller_id": "test_seller_123",
        "merchantStoreRegion": "US",
        "temuProductType": "local",
        "imgOption": {
            "imgUrl": "https://example.com/test-image.jpg",
            "temuImgBizType": 1,
            "temuImgSizeMode": 2,
            "temuCateId": "123"
        }
    }
    
    print("=== 使用模拟数据测试 ===")
    print(f"测试消息: {json.dumps(test_message, indent=2, ensure_ascii=False)}")
    print("\n开始处理...")
    
    try:
        result = process_one_sqs_message(mock_dynamodb, test_message)
        print(f"\n处理成功!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"\n处理失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")


if __name__ == "__main__":
    print("选择测试方式:")
    print("1. 测试真实DynamoDB连接")
    print("2. 使用模拟数据测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_single_message()
    elif choice == "2":
        test_with_mock_data()
    else:
        print("无效选择，使用模拟数据测试")
        test_with_mock_data()
