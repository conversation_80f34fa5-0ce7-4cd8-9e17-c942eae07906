#!/usr/bin/env python3
import os
import sys
import json
import time
import boto3
import requests
from dotenv import load_dotenv
from typing import Optional, Dict, Any


def log_info(message: str, **kwargs):
    """简单的日志输出"""
    print(f"[INFO] {message}", kwargs if kwargs else "")


def log_error(message: str, **kwargs):
    """简单的错误日志输出"""
    print(f"[ERROR] {message}", kwargs if kwargs else "")


def get_sqs_message(sqs_client, queue_url: str) -> Optional[Dict[str, Any]]:
    """从SQS获取单个消息"""
    try:
        response = sqs_client.receive_message(
            QueueUrl=queue_url,
            MaxNumberOfMessages=1,
            WaitTimeSeconds=20  # 长轮询
        )

        messages = response.get('Messages', [])
        if messages:
            return messages[0]
        return None
    except Exception as e:
        log_error("获取SQS消息失败", error=str(e))
        return None


def delete_sqs_message(sqs_client, queue_url: str, receipt_handle: str) -> bool:
    """删除SQS消息"""
    try:
        sqs_client.delete_message(
            QueueUrl=queue_url,
            ReceiptHandle=receipt_handle
        )
        return True
    except Exception as e:
        log_error("删除SQS消息失败", error=str(e))
        return False


def get_dynamodb_record(dynamodb_client, table_name: str, seller_id: str) -> Optional[str]:
    """从DynamoDB获取token"""
    try:
        table = dynamodb_client.Table(table_name)
        response = table.get_item(Key={'seller_id': seller_id})

        if 'Item' in response:
            return response['Item'].get('token')
        return None

    except Exception as e:
        log_error("查询DynamoDB失败", table_name=table_name, seller_id=seller_id, error=str(e))
        return None


def send_webhook(webhook_url: str, payload: Dict[str, Any]) -> bool:
    """发送webhook通知"""
    try:
        response = requests.post(
            webhook_url,
            json=payload,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        response.raise_for_status()
        log_info("Webhook发送成功", url=webhook_url, status=response.status_code)
        return True

    except Exception as e:
        log_error("Webhook发送失败", url=webhook_url, error=str(e))
        return False


def process_message(sqs_client, dynamodb_client, queue_url: str, message: Dict[str, Any]) -> bool:
    """处理单个消息"""
    message_id = message.get('MessageId', 'unknown')
    receipt_handle = message.get('ReceiptHandle')

    try:
        # 1. 解析消息
        body = json.loads(message['Body'])
        webhook_url = body['webhook']
        env = body['env']
        seller_id = body['seller_id']
        merchant_store_region = body['merchantStoreRegion']
        img_option = body['imgOption']

        log_info("开始处理消息", message_id=message_id, seller_id=seller_id, env=env)

        # 2. 查询DynamoDB获取token
        table_name = f"temuProduct_{env}"
        token = get_dynamodb_record(dynamodb_client, table_name, seller_id)

        if not token:
            # 发送失败通知
            error_payload = {
                "success": False,
                "seller_id": seller_id,
                "env": env,
                "merchantStoreRegion": merchant_store_region,
                "imgOption": img_option,
                "error": f"未找到商户记录或token为空: seller_id={seller_id}, env={env}"
            }
            send_webhook(webhook_url, error_payload)
            return False

        # 3. 发送成功通知
        success_payload = {
            "success": True,
            "seller_id": seller_id,
            "env": env,
            "merchantStoreRegion": merchant_store_region,
            "imgOption": img_option,
            "token": token,
            "message": "处理成功"
        }

        if not send_webhook(webhook_url, success_payload):
            log_error("Webhook发送失败", message_id=message_id)
            return False

        # 4. 删除消息
        if receipt_handle:
            delete_sqs_message(sqs_client, queue_url, receipt_handle)

        log_info("消息处理完成", message_id=message_id, seller_id=seller_id)
        return True

    except json.JSONDecodeError as e:
        log_error("消息JSON解析失败", message_id=message_id, error=str(e))
        return False

    except KeyError as e:
        log_error("消息字段缺失", message_id=message_id, missing_field=str(e))
        return False

    except Exception as e:
        log_error("消息处理异常", message_id=message_id, error=str(e))

        # 尝试发送错误通知
        try:
            body = json.loads(message['Body'])
            error_payload = {
                "success": False,
                "seller_id": body.get('seller_id', ''),
                "env": body.get('env', ''),
                "merchantStoreRegion": body.get('merchantStoreRegion', ''),
                "imgOption": body.get('imgOption', {}),
                "error": f"处理异常: {str(e)}"
            }
            send_webhook(body.get('webhook', ''), error_payload)
        except:
            pass

        return False


def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()

    # 获取配置
    aws_region = os.getenv('AWS_REGION', 'us-east-1')
    sqs_queue_url = os.getenv('SQS_QUEUE_URL')
    poll_interval = int(os.getenv('POLL_INTERVAL', '30'))

    if not sqs_queue_url:
        log_error("SQS_QUEUE_URL环境变量未设置")
        sys.exit(1)

    log_info("启动Temu图片处理服务",
             aws_region=aws_region,
             queue_url=sqs_queue_url,
             poll_interval=poll_interval)

    # 初始化AWS客户端
    try:
        sqs_client = boto3.client('sqs', region_name=aws_region)
        dynamodb_client = boto3.resource('dynamodb', region_name=aws_region)
        log_info("AWS客户端初始化成功")
    except Exception as e:
        log_error("AWS客户端初始化失败", error=str(e))
        sys.exit(1)

    # 主循环
    try:
        while True:
            try:
                # 获取单个消息
                message = get_sqs_message(sqs_client, sqs_queue_url)

                if message:
                    # 处理消息
                    success = process_message(sqs_client, dynamodb_client, sqs_queue_url, message)
                    if success:
                        log_info("消息处理成功")
                    else:
                        log_error("消息处理失败")

                    # 短暂休息后继续
                    time.sleep(1)
                else:
                    # 没有消息，等待
                    log_info("没有新消息，等待中...")
                    time.sleep(poll_interval)

            except KeyboardInterrupt:
                log_info("接收到停止信号，正在退出...")
                break
            except Exception as e:
                log_error("处理循环异常", error=str(e))
                time.sleep(poll_interval)

    except Exception as e:
        log_error("程序异常退出", error=str(e))
        sys.exit(1)

    log_info("程序正常退出")


if __name__ == "__main__":
    main()