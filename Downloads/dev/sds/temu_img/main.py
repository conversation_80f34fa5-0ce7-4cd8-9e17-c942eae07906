#!/usr/bin/env python3
import os
import sys
import json
import time
import logging
import hashlib
import boto3
import requests
from dotenv import load_dotenv
from typing import Optional, Dict, Any


class MerchantDataError(Exception):
    """商户数据相关异常"""
    pass


def setup_logging():
    """设置日志配置"""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    logging.basicConfig(
        level=getattr(logging, log_level, logging.INFO),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 设置boto3日志级别
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


logger = logging.getLogger(__name__)


def create_temu_sign(request_body: Dict[str, Any], app_secret: str) -> str:
    # 按key的ASCII值排序
    sorted_items = sorted(request_body.items())

    # 拼接字符串
    concatenated_string = app_secret
    for key, value in sorted_items:
        if value is None:
            continue
        concatenated_string += str(key) + str(value)

    concatenated_string += app_secret

    # 计算MD5签名并转为大写
    sign = hashlib.md5(concatenated_string.encode('utf-8')).hexdigest().upper()
    return sign


def prepare_temu_request(request_body: Optional[Dict[str, Any]],
                        request_type: str,
                        version: str,
                        app_key: str,
                        access_token: str,
                        app_secret: str) -> Dict[str, Any]:
    """
    准备Temu API请求（包含签名）

    Args:
        request_body: 业务请求体数据
        request_type: 请求类型
        version: 版本号
        app_key: 应用密钥
        access_token: 访问令牌
        app_secret: 应用秘钥

    Returns:
        签名后的完整请求体
    """
    if request_body is None:
        request_body = {}

    # 复制请求体，避免修改原始数据
    signed_body = request_body.copy()

    # 添加通用请求参数
    signed_body.update({
        "type": request_type,
        "dataType": "JSON",
        "version": version,
        "timestamp": int(time.time()),  # 当前时间戳（秒）
        "appKey": app_key,
        "accessToken": access_token
    })

    # 生成签名
    sign = create_temu_sign(signed_body, app_secret)
    signed_body["sign"] = sign

    return signed_body


def get_sqs_message(sqs_client, queue_url: str) -> Optional[Dict[str, Any]]:
    """从SQS获取单个消息"""
    try:
        response = sqs_client.receive_message(
            QueueUrl=queue_url,
            MaxNumberOfMessages=1,
            WaitTimeSeconds=20  # 长轮询
        )

        messages = response.get('Messages', [])
        if messages:
            logger.debug("成功获取到SQS消息")
            return messages[0]
        return None
    except Exception as e:
        logger.error(f"获取SQS消息失败: {str(e)}")
        return None


def delete_sqs_message(sqs_client, queue_url: str, receipt_handle: str) -> bool:
    """删除SQS消息"""
    try:
        sqs_client.delete_message(
            QueueUrl=queue_url,
            ReceiptHandle=receipt_handle
        )
        logger.info("SQS消息删除成功")
        return True
    except Exception as e:
        logger.error(f"删除SQS消息失败: {str(e)}")
        return False


def get_dynamodb_record(dynamodb_client, env: str, mall_id: str) -> Dict[str, str]:
    # 根据env映射到实际的table环境
    if env in ['test1', 'test2', 'test3']:
        table_env = 'test'
    elif env in ['sim', 'prod']:
        table_env = 'prod'
    else:
        logger.error(f"不支持的环境: env={env}")
        raise MerchantDataError(f"不支持的环境: {env}")

    # 构建表名
    table_prefix = os.getenv('DYNAMODB_TABLE_PREFIX', 'temuProduct')
    table_name = f"{table_prefix}_{table_env}"

    try:
        table = dynamodb_client.Table(table_name)
        response = table.get_item(Key={'mallId': mall_id})

        if 'Item' not in response:
            logger.warning(f"商户记录不存在: mallId={mall_id}, table={table_name}")
            raise MerchantDataError(f"dynamodb表名{table_name} Key{mall_id} 授权数据 查询不存在或者授权失效")

        item = response['Item']
        result = {
            'accessToken': item.get('accessToken', ''),
            'appKey': item.get('appKey', ''),
            'appSecret': item.get('appSecret', ''),
            'authStatus': item.get('authStatus', '')
        }

        # 检查必要字段是否为空
        if not all([result['accessToken'], result['appKey'], result['appSecret']]):
            logger.warning(f"商户必要字段为空: mallId={mall_id}, table={table_name}")
            raise MerchantDataError(f"dynamodb表名{table_name} Key{mall_id} 授权数据 查询不存在或者授权失效")

        # 检查authStatus状态
        if result['authStatus'] != 'SUCCESS':
            logger.warning(f"商户授权状态异常: mallId={mall_id}, authStatus={result['authStatus']}")
            raise MerchantDataError(f"dynamodb表名{table_name} Key{mall_id} 授权数据 查询不存在或者授权失效")

        logger.info(f"成功获取商户数据: mallId={mall_id}, table={table_name}, authStatus={result['authStatus']}")
        return result

    except MerchantDataError:
        # 重新抛出自定义异常
        raise
    except Exception as e:
        logger.error(f"查询DynamoDB失败: table={table_name}, mallId={mall_id}, error={str(e)}")
        raise MerchantDataError(f"dynamodb表名{table_name} Key{mall_id} 授权数据 查询不存在或者授权失效")


def call_temu_api(endpoint: str,
                  request_body: Optional[Dict[str, Any]],
                  request_type: str,
                  version: str,
                  app_key: str,
                  access_token: str,
                  app_secret: str) -> Optional[Dict[str, Any]]:
    try:
        # 准备签名后的请求数据
        signed_request = prepare_temu_request(
            request_body=request_body,
            request_type=request_type,
            version=version,
            app_key=app_key,
            access_token=access_token,
            app_secret=app_secret
        )

        logger.info(f"调用Temu API: endpoint={endpoint}, type={request_type}")
        logger.debug(f"请求参数: {json.dumps(signed_request, ensure_ascii=False)}")

        # 发送HTTP POST请求
        response = requests.post(
            endpoint,
            json=signed_request,
            timeout=60,
            headers={
                'Content-Type': 'application/json'
            }
        )

        response.raise_for_status()

        # 解析响应
        response_data = response.json()
        logger.info(f"Temu API调用成功: status={response.status_code}")
        logger.debug(f"响应数据: {json.dumps(response_data, ensure_ascii=False)}")

        return response_data

    except requests.exceptions.Timeout:
        logger.error(f"Temu API请求超时: endpoint={endpoint}")
        return None

    except requests.exceptions.ConnectionError:
        logger.error(f"Temu API连接失败: endpoint={endpoint}")
        return None

    except requests.exceptions.HTTPError as e:
        logger.error(f"Temu API HTTP错误: {e.response.status_code} - {endpoint}")
        return None
    except Exception as e:
        logger.error(f"Temu API调用失败: endpoint={endpoint}, error={str(e)}")
        return None


def send_webhook(webhook_url: str, payload: Dict[str, Any]) -> bool:
    """发送webhook通知"""
    try:
        webhook_timeout = int(os.getenv('WEBHOOK_TIMEOUT', '30'))
        response = requests.post(
            webhook_url,
            json=payload,
            timeout=webhook_timeout,
            headers={'Content-Type': 'application/json'}
        )
        response.raise_for_status()
        logger.info(f"Webhook发送成功: url={webhook_url}, status={response.status_code}")
        return True

    except Exception as e:
        logger.error(f"Webhook发送失败: url={webhook_url}, error={str(e)}")
        return False


def process_message(sqs_client, dynamodb_client, queue_url: str, message: Dict[str, Any]) -> bool:
    """处理单个消息"""
    message_id = message.get('MessageId', 'unknown')
    receipt_handle = message.get('ReceiptHandle')

    try:
        # 1. 解析消息
        body = json.loads(message['Body'])
        webhook_url = body['webhook']
        env = body['env']
        mall_id = body['seller_id']  # 使用seller_id作为mallId
        merchant_store_region = body['merchantStoreRegion']
        temu_product_type = body['temuProductType']
        img_option = body['imgOption']

        logger.info(f"开始处理消息: message_id={message_id}, seller_id={mall_id}, env={env}, temuProductType={temu_product_type}")

        # 2. 查询DynamoDB获取商户数据
        try:
            merchant_data = get_dynamodb_record(dynamodb_client, env, mall_id)
        except MerchantDataError as e:
            # 发送失败通知 - 商户数据异常
            error_payload = {
                "success": False,
                "error": str(e),
                "msgContent": body  # 返回原始SQS消息内容
            }
            send_webhook(webhook_url, error_payload)
            return False

        # 3. 处理temuProductType=local的情况
        if temu_product_type == "local":
            logger.info(f"检测到temuProductType=local，准备调用Temu API")

            # 准备Temu API请求参数
            temu_request_body = {
                "fileUrl": img_option.get('imgUrl', ''),
                "scalingType": img_option.get('temuImgSizeMode', 0),
                "compressionType": 0,
                "formatConversionType": 0
            }

            # 调用Temu API
            temu_endpoint = "https://api-sg.temu.com/bg/api"  # 根据实际情况修改
            temu_response = call_temu_api(
                endpoint=temu_endpoint,
                request_body=temu_request_body,
                request_type="bg.local.goods.image.upload",
                version="1.0",
                app_key=merchant_data['appKey'],
                access_token=merchant_data['accessToken'],
                app_secret=merchant_data['appSecret']
            )

            if temu_response is None:
                # Temu API调用失败
                error_payload = {
                    "success": False,
                    "error": "Temu API调用失败",
                    "msgContent": body
                }
                send_webhook(webhook_url, error_payload)
                return False

            logger.info(f"Temu API调用成功: {temu_response}")

        # 4. 发送成功通知
        success_payload = {
            "success": True,
            "accessToken": merchant_data['accessToken'],
            "appKey": merchant_data['appKey'],
            "appSecret": merchant_data['appSecret'],
            "authStatus": merchant_data['authStatus'],
            "msgContent": body  # 返回原始SQS消息内容
        }

        # 如果调用了Temu API，添加响应结果
        if temu_product_type == "local" and 'temu_response' in locals():
            success_payload["temuApiResponse"] = temu_response

        if not send_webhook(webhook_url, success_payload):
            logger.error(f"Webhook发送失败: message_id={message_id}")
            return False

        # 4. 删除消息
        if receipt_handle:
            delete_sqs_message(sqs_client, queue_url, receipt_handle)

        logger.info(f"消息处理完成: message_id={message_id}, seller_id={mall_id}")
        return True

    except json.JSONDecodeError as e:
        logger.error(f"消息JSON解析失败: message_id={message_id}, error={str(e)}")
        return False

    except KeyError as e:
        logger.error(f"消息字段缺失: message_id={message_id}, missing_field={str(e)}")
        return False

    except Exception as e:
        logger.error(f"消息处理异常: message_id={message_id}, error={str(e)}")

        # 尝试发送错误通知
        try:
            body = json.loads(message['Body'])
            error_payload = {
                "success": False,
                "error": f"处理异常: {str(e)}",
                "msgContent": body  # 返回原始SQS消息内容
            }
            send_webhook(body.get('webhook', ''), error_payload)
        except Exception as webhook_error:
            logger.error(f"发送错误通知失败: {str(webhook_error)}")

        return False


def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()

    # 设置日志
    setup_logging()

    # 获取配置
    aws_region = os.getenv('AWS_REGION', 'us-east-1')
    sqs_queue_url = os.getenv('SQS_QUEUE_URL')
    poll_interval = int(os.getenv('POLL_INTERVAL', '30'))

    if not sqs_queue_url:
        logger.error("SQS_QUEUE_URL环境变量未设置")
        sys.exit(1)

    logger.info(f"启动Temu图片处理服务: aws_region={aws_region}, queue_url={sqs_queue_url}, poll_interval={poll_interval}")

    # 初始化AWS客户端
    try:
        sqs_client = boto3.client('sqs', region_name=aws_region)
        dynamodb_client = boto3.resource('dynamodb', region_name=aws_region)
        logger.info("AWS客户端初始化成功")
    except Exception as e:
        logger.error(f"AWS客户端初始化失败: {str(e)}")
        sys.exit(1)

    # 主循环
    try:
        logger.info("开始消息处理循环")
        while True:
            try:
                # 获取单个消息
                message = get_sqs_message(sqs_client, sqs_queue_url)

                if message:
                    # 处理消息
                    success = process_message(sqs_client, dynamodb_client, sqs_queue_url, message)
                    if success:
                        logger.info("消息处理成功")
                    else:
                        logger.error("消息处理失败")

                    # 短暂休息后继续
                    time.sleep(1)
                else:
                    # 没有消息，等待
                    logger.debug("没有新消息，等待中...")
                    time.sleep(poll_interval)

            except KeyboardInterrupt:
                logger.info("接收到停止信号，正在退出...")
                break
            except Exception as e:
                logger.error(f"处理循环异常: {str(e)}")
                time.sleep(poll_interval)

    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
        sys.exit(1)

    logger.info("程序正常退出")


if __name__ == "__main__":
    main()