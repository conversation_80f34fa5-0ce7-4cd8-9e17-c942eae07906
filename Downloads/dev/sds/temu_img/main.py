#!/usr/bin/env python3
"""
Temu图片处理服务主程序
从SQS读取消息，查询DynamoDB获取token，调用webhook通知商户
"""
import sys
import argparse
from dotenv import load_dotenv

from logger_config import configure_logging, get_logger
from message_processor import MessageProcessor
from config import settings


def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()

    # 配置日志
    configure_logging()
    logger = get_logger(__name__)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Temu图片处理服务')
    parser.add_argument(
        '--mode',
        choices=['single', 'continuous'],
        default='continuous',
        help='运行模式: single(单次处理) 或 continuous(持续运行)'
    )
    parser.add_argument(
        '--max-messages',
        type=int,
        default=1,
        help='单次处理的最大消息数量（默认为1）'
    )
    parser.add_argument(
        '--poll-interval',
        type=int,
        default=30,
        help='持续模式下的轮询间隔（秒）'
    )

    args = parser.parse_args()

    # 验证必要的配置
    if not settings.sqs_queue_url:
        logger.error("SQS_QUEUE_URL未配置，请检查环境变量")
        sys.exit(1)

    logger.info(
        "启动Temu图片处理服务",
        mode=args.mode,
        max_messages=args.max_messages,
        poll_interval=args.poll_interval,
        sqs_queue_url=settings.sqs_queue_url,
        aws_region=settings.aws_region
    )

    try:
        # 创建消息处理器
        processor = MessageProcessor()

        if args.mode == 'single':
            # 单次处理模式
            if args.max_messages == 1:
                # 处理单个消息
                success_count, total_count = processor.process_single_message_from_queue()
                logger.info(
                    "单次处理完成",
                    success_count=success_count,
                    total_count=total_count
                )
            else:
                # 批量处理（保留兼容性）
                success_count, total_count = processor.process_messages_batch(args.max_messages)
                logger.info(
                    "批量处理完成",
                    success_count=success_count,
                    total_count=total_count
                )
        else:
            # 持续运行模式
            processor.run_continuous(args.poll_interval)

    except KeyboardInterrupt:
        logger.info("接收到停止信号，正在退出...")
    except Exception as e:
        logger.error("程序异常退出", error=str(e), exc_info=True)
        sys.exit(1)

    logger.info("程序正常退出")


if __name__ == "__main__":
    main()