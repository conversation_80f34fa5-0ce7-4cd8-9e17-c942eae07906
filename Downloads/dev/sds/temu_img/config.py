"""
配置管理模块
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # AWS配置
    aws_region: str = "us-east-1"
    sqs_queue_url: str = ""
    dynamodb_table_prefix: str = "temuProduct"
    
    # 日志配置
    log_level: str = "INFO"
    
    # 重试配置
    max_retries: int = 3
    retry_delay: int = 1
    
    # HTTP配置
    webhook_timeout: int = 30
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
