"""
数据模型定义
"""
from typing import Optional
from pydantic import BaseModel, HttpUrl


class ImgOption(BaseModel):
    """图片选项"""
    temuProductType: str  # local/temu_semi
    imgUrl: str
    temuImgBizType: int
    temuImgSizeMode: int
    temuCateId: str


class SQSMessage(BaseModel):
    """SQS消息结构"""
    webhook: HttpUrl
    env: str  # test/test2/test3/sim/prod
    seller_id: str
    merchantStoreRegion: str
    imgOption: ImgOption


class TemuProductRecord(BaseModel):
    """DynamoDB中的商户产品记录"""
    seller_id: str
    env: str
    token: str
    # 可以根据实际DynamoDB表结构添加更多字段


class WebhookResponse(BaseModel):
    """Webhook响应"""
    success: bool
    message: Optional[str] = None
    data: Optional[dict] = None
