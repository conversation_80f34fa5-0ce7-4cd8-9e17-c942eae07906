"""
Webhook通知服务
"""
import requests
import structlog
from typing import Dict, Any, Optional
from tenacity import retry, stop_after_attempt, wait_exponential

from models import WebhookResponse, SQSMessage, TemuProductRecord
from config import settings

logger = structlog.get_logger()


class WebhookHandler:
    """Webhook通知处理器"""
    
    def __init__(self):
        """初始化HTTP会话"""
        self.session = requests.Session()
        self.session.timeout = settings.webhook_timeout
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'TemuImageProcessor/1.0'
        })
        
        logger.info("Webhook处理器初始化成功")
    
    def _prepare_webhook_payload(
        self, 
        message: SQSMessage, 
        product_record: TemuProductRecord,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        准备webhook回调数据
        
        Args:
            message: 原始SQS消息
            product_record: 商户产品记录
            success: 处理是否成功
            error_message: 错误信息（如果有）
            
        Returns:
            webhook回调数据
        """
        payload = {
            "success": success,
            "seller_id": message.seller_id,
            "env": message.env,
            "merchantStoreRegion": message.merchantStoreRegion,
            "imgOption": message.imgOption.dict(),
            "timestamp": None,  # 可以添加时间戳
        }
        
        if success:
            payload["token"] = product_record.token
            payload["message"] = "处理成功"
        else:
            payload["error"] = error_message or "处理失败"
            
        return payload
    
    @retry(
        stop=stop_after_attempt(settings.max_retries),
        wait=wait_exponential(multiplier=settings.retry_delay, min=1, max=10)
    )
    def send_webhook_notification(
        self, 
        webhook_url: str, 
        message: SQSMessage,
        product_record: Optional[TemuProductRecord] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> WebhookResponse:
        """
        发送webhook通知
        
        Args:
            webhook_url: 回调地址
            message: 原始SQS消息
            product_record: 商户产品记录
            success: 处理是否成功
            error_message: 错误信息
            
        Returns:
            webhook响应
        """
        try:
            # 准备回调数据
            if success and product_record:
                payload = self._prepare_webhook_payload(message, product_record, success)
            else:
                # 创建一个临时的product_record用于错误回调
                temp_record = TemuProductRecord(
                    seller_id=message.seller_id,
                    env=message.env,
                    token=""
                )
                payload = self._prepare_webhook_payload(
                    message, temp_record, success=False, error_message=error_message
                )
            
            logger.info("发送webhook通知", webhook_url=webhook_url, success=success)
            
            # 发送HTTP POST请求
            response = self.session.post(
                webhook_url,
                json=payload,
                timeout=settings.webhook_timeout
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 尝试解析响应JSON
            try:
                response_data = response.json()
            except ValueError:
                response_data = {"raw_response": response.text}
            
            webhook_response = WebhookResponse(
                success=True,
                message="Webhook通知发送成功",
                data=response_data
            )
            
            logger.info(
                "Webhook通知发送成功", 
                webhook_url=webhook_url,
                status_code=response.status_code,
                response_data=response_data
            )
            
            return webhook_response
            
        except requests.exceptions.Timeout:
            error_msg = f"Webhook请求超时: {webhook_url}"
            logger.error(error_msg)
            return WebhookResponse(success=False, message=error_msg)
            
        except requests.exceptions.ConnectionError:
            error_msg = f"Webhook连接失败: {webhook_url}"
            logger.error(error_msg)
            return WebhookResponse(success=False, message=error_msg)
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"Webhook HTTP错误: {e.response.status_code} - {webhook_url}"
            logger.error(error_msg, status_code=e.response.status_code)
            return WebhookResponse(success=False, message=error_msg)
            
        except Exception as e:
            error_msg = f"Webhook发送失败: {str(e)}"
            logger.error(error_msg, error=str(e))
            return WebhookResponse(success=False, message=error_msg)
    
    def close(self):
        """关闭HTTP会话"""
        self.session.close()
        logger.info("Webhook处理器已关闭")
