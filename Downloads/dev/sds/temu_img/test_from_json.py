#!/usr/bin/env python3
"""
从JSON文件加载测试消息进行测试
"""
import json
import boto3
from dotenv import load_dotenv

# 导入主程序模块
from main import process_one_sqs_message, setup_logging, logger

def load_test_messages():
    """加载测试消息"""
    try:
        with open('test_messages.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 找不到test_messages.json文件")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON文件格式错误: {e}")
        return None

def test_message_from_json():
    """从JSON文件测试消息"""
    # 加载环境变量和设置日志
    load_dotenv()
    setup_logging()
    
    # 加载测试消息
    test_messages = load_test_messages()
    if not test_messages:
        return
    
    print("=== 可用的测试消息 ===")
    for key in test_messages.keys():
        print(f"- {key}")
    
    # 选择测试消息
    message_key = input("\n请输入要测试的消息名称: ").strip()
    
    if message_key not in test_messages:
        print(f"❌ 找不到消息: {message_key}")
        return
    
    test_message = test_messages[message_key]
    
    print(f"\n=== 测试消息: {message_key} ===")
    print(f"消息内容: {json.dumps(test_message, indent=2, ensure_ascii=False)}")
    
    try:
        # 初始化DynamoDB客户端
        aws_region = os.getenv('AWS_REGION', 'us-east-1')
        dynamodb_client = boto3.resource('dynamodb', region_name=aws_region)
        
        print("\n🚀 开始处理...")
        
        # 处理消息
        result = process_one_sqs_message(dynamodb_client, test_message)
        
        print(f"\n✅ 处理成功!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")

if __name__ == "__main__":
    test_message_from_json()
