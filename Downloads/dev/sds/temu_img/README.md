# Temu图片处理服务

一个简化的AWS SQS消息处理服务，用于处理Temu图片相关的业务逻辑。

## 功能特性

- 循环从AWS SQS队列读取消息（每次1个）
- 根据seller_id和env查询DynamoDB获取token
- 调用商户webhook地址进行通知
- 失败时自动发送错误通知到webhook
- 简单的日志记录

## 项目结构

```
temu_img/
├── main.py                    # 主程序（包含所有逻辑）
├── requirements.txt          # Python依赖
├── .env.example             # 环境变量示例
└── README.md                # 项目文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入实际的配置值：

```bash
# AWS配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# SQS配置
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/your-queue-name

# 轮询间隔（秒）
POLL_INTERVAL=30
```

### 3. AWS权限配置

确保AWS凭证具有以下权限：

- SQS: `ReceiveMessage`, `DeleteMessage`
- DynamoDB: `GetItem` (对temuProduct_*表)

## 使用方法

### 运行程序

直接运行程序，它会持续循环处理消息：
```bash
python main.py
```

程序会：
1. 循环从SQS队列获取消息（每次1个）
2. 处理消息并调用webhook
3. 如果没有消息，等待30秒后继续轮询
4. 按Ctrl+C停止程序

### 环境变量配置

可以通过环境变量调整轮询间隔：
```bash
POLL_INTERVAL=60 python main.py  # 60秒轮询一次
```

## 消息格式

SQS消息应该包含以下JSON结构：

```json
{
    "webhook": "https://merchant.example.com/callback",
    "env": "test",
    "seller_id": "seller123",
    "merchantStoreRegion": "US",
    "imgOption": {
        "temuProductType": "local",
        "imgUrl": "https://example.com/image.jpg",
        "temuImgBizType": 1,
        "temuImgSizeMode": 1,
        "temuCateId": "123"
    }
}
```

## DynamoDB表结构

程序会查询名为 `{DYNAMODB_TABLE_PREFIX}_{env}` 的DynamoDB表，例如：
- `temuProduct_test`
- `temuProduct_prod`

表应该包含以下字段：
- `seller_id` (主键)
- `accessToken` (访问令牌)
- `appKey` (应用密钥)
- `appSecret` (应用秘钥)
- `authStatus` (授权状态，必须为"SUCCESS"才能正常处理)

## Webhook回调

成功处理后，会向商户webhook地址发送POST请求：

```json
{
    "success": true,
    "seller_id": "seller123",
    "env": "test",
    "merchantStoreRegion": "US",
    "imgOption": { ... },
    "accessToken": "access-token-here",
    "appKey": "app-key-here",
    "appSecret": "app-secret-here",
    "authStatus": "SUCCESS",
    "message": "处理成功"
}
```

失败时的回调：

```json
{
    "success": false,
    "seller_id": "seller123",
    "env": "test",
    "merchantStoreRegion": "US",
    "imgOption": { ... },
    "error": "错误描述"
}
```

常见的错误情况：
- `未找到商户记录: seller_id=xxx, env=xxx` - DynamoDB中不存在该商户记录
- `商户必要字段为空: seller_id=xxx, env=xxx` - accessToken、appKey或appSecret为空
- `商户授权状态异常: authStatus=xxx, seller_id=xxx, env=xxx` - authStatus不等于"SUCCESS"

## 运行测试

```bash
python test_message_processor.py
```

## 日志

程序使用结构化日志，输出JSON格式的日志信息，便于日志聚合和分析。

## 错误处理

- 自动重试机制（可配置重试次数和延迟）
- 详细的错误日志记录
- 失败消息的webhook通知
- 优雅的异常处理

## 监控建议

- 监控SQS队列深度
- 监控处理成功率
- 监控webhook响应时间
- 设置DynamoDB读取容量告警

## 部署建议

- 使用Docker容器化部署
- 配置适当的AWS IAM角色
- 设置健康检查
- 配置日志收集和监控
