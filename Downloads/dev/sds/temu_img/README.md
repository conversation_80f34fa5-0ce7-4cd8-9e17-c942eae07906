# Temu图片处理服务

一个完整的AWS SQS消息处理服务，用于处理Temu图片相关的业务逻辑。

## 功能特性

- 从AWS SQS队列读取消息
- 根据seller_id和env查询DynamoDB获取token
- 调用商户webhook地址进行通知
- 完整的错误处理和重试机制
- 结构化日志记录
- 支持单次处理和持续运行模式

## 项目结构

```
temu_img/
├── main.py                    # 主程序入口
├── config.py                  # 配置管理
├── models.py                  # 数据模型定义
├── sqs_handler.py            # SQS消息处理器
├── dynamodb_handler.py       # DynamoDB数据访问层
├── webhook_handler.py        # Webhook通知服务
├── message_processor.py      # 主业务逻辑处理器
├── logger_config.py          # 日志配置
├── exceptions.py             # 自定义异常类
├── test_message_processor.py # 单元测试
├── requirements.txt          # Python依赖
├── .env.example             # 环境变量示例
└── README.md                # 项目文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入实际的配置值：

```bash
# AWS配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# SQS配置
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/your-queue-name

# DynamoDB配置
DYNAMODB_TABLE_PREFIX=temuProduct

# 日志配置
LOG_LEVEL=INFO

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1

# HTTP配置
WEBHOOK_TIMEOUT=30
```

### 3. AWS权限配置

确保AWS凭证具有以下权限：

- SQS: `ReceiveMessage`, `DeleteMessage`
- DynamoDB: `GetItem` (对temuProduct_*表)

## 使用方法

### 单次处理模式

处理一批消息后退出：
```bash
python main.py --mode single --max-messages 10
```

### 持续运行模式

持续监听和处理消息：
```bash
python main.py --mode continuous --poll-interval 30
```

### 命令行参数

- `--mode`: 运行模式 (`single` 或 `continuous`)
- `--max-messages`: 单次处理的最大消息数量 (默认: 10)
- `--poll-interval`: 持续模式下的轮询间隔，单位秒 (默认: 30)

## 消息格式

SQS消息应该包含以下JSON结构：

```json
{
    "webhook": "https://merchant.example.com/callback",
    "env": "test",
    "seller_id": "seller123",
    "merchantStoreRegion": "US",
    "imgOption": {
        "temuProductType": "local",
        "imgUrl": "https://example.com/image.jpg",
        "temuImgBizType": 1,
        "temuImgSizeMode": 1,
        "temuCateId": "123"
    }
}
```

## DynamoDB表结构

程序会查询名为 `temuProduct_{env}` 的DynamoDB表，例如：
- `temuProduct_test`
- `temuProduct_prod`

表应该包含以下字段：
- `seller_id` (主键)
- `token` (商户token)

## Webhook回调

成功处理后，会向商户webhook地址发送POST请求：

```json
{
    "success": true,
    "seller_id": "seller123",
    "env": "test",
    "merchantStoreRegion": "US",
    "imgOption": { ... },
    "token": "merchant-token-here",
    "message": "处理成功"
}
```

失败时的回调：

```json
{
    "success": false,
    "seller_id": "seller123",
    "env": "test",
    "merchantStoreRegion": "US",
    "imgOption": { ... },
    "error": "错误描述"
}
```

## 运行测试

```bash
python test_message_processor.py
```

## 日志

程序使用结构化日志，输出JSON格式的日志信息，便于日志聚合和分析。

## 错误处理

- 自动重试机制（可配置重试次数和延迟）
- 详细的错误日志记录
- 失败消息的webhook通知
- 优雅的异常处理

## 监控建议

- 监控SQS队列深度
- 监控处理成功率
- 监控webhook响应时间
- 设置DynamoDB读取容量告警

## 部署建议

- 使用Docker容器化部署
- 配置适当的AWS IAM角色
- 设置健康检查
- 配置日志收集和监控
