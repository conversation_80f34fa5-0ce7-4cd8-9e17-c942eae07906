# Temu图片处理服务

一个简化的AWS SQS消息处理服务，用于处理Temu图片相关的业务逻辑。

## 功能特性

- 循环从AWS SQS队列读取消息（每次1个）
- 根据seller_id和env查询DynamoDB获取商户数据
- 自定义异常处理机制，精确识别各种错误类型
- 调用商户webhook地址进行通知
- 失败时自动发送详细错误信息到webhook
- 完整的日志记录和错误追踪
- Temu API调用支持（包含参数加密和签名）
- 智能处理：支持temuProductType=local和temu_semi，自动调用对应的Temu API
- 统一的Temu API异常处理和错误响应

## 项目结构

```
temu_img/
├── main.py                    # 主程序（包含所有逻辑和Temu API签名）
├── requirements.txt          # Python依赖
├── .env.example             # 环境变量示例
└── README.md                # 项目文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入实际的配置值：

```bash
# AWS配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# SQS配置
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/your-queue-name

# 轮询间隔（秒）
POLL_INTERVAL=30
```

### 3. AWS权限配置

确保AWS凭证具有以下权限：

- SQS: `ReceiveMessage`, `DeleteMessage`
- DynamoDB: `GetItem` (对temuProduct_*表)

## 使用方法

### 运行程序

直接运行程序，它会持续循环处理消息：
```bash
python main.py
```

程序会：
1. 循环从SQS队列获取消息（每次1个）
2. 处理消息并调用webhook
3. 如果没有消息，等待30秒后继续轮询
4. 按Ctrl+C停止程序

### 环境变量配置

可以通过环境变量调整轮询间隔：
```bash
POLL_INTERVAL=60 python main.py  # 60秒轮询一次
```

## 测试单个消息

### 方法1：使用内置测试功能

```bash
python main.py test
```

### 方法2：使用独立测试脚本

```bash
python test_single_message.py
```

### 方法3：使用JSON文件测试

```bash
python test_from_json.py
```

### 测试文件说明

- `test_single_message.py`: 独立的测试脚本，支持模拟数据测试
- `test_from_json.py`: 从JSON文件加载测试消息
- `test_messages.json`: 预定义的测试消息样例

## 消息格式

SQS消息应该包含以下JSON结构：

```json
{
    "webhook": "https://merchant.example.com/callback",
    "env": "test",
    "seller_id": "seller123",
    "merchantStoreRegion": "US",
    "temuProductType": "local",
    "imgOption": {
        "imgUrl": "https://example.com/image.jpg",
        "temuImgBizType": 1,
        "temuImgSizeMode": 1,
        "temuCateId": "123"
    }
}
```

## DynamoDB表结构

程序会根据消息中的env字段映射到实际的DynamoDB表：

**环境映射规则：**
- `test1/test2/test3` → `{DYNAMODB_TABLE_PREFIX}_test`
- `sim/prod` → `{DYNAMODB_TABLE_PREFIX}_prod`

**实际表名示例：**
- 消息env为`test1` → 查询表`temuProduct_test`
- 消息env为`test2` → 查询表`temuProduct_test`
- 消息env为`test3` → 查询表`temuProduct_test`
- 消息env为`sim` → 查询表`temuProduct_prod`
- 消息env为`prod` → 查询表`temuProduct_prod`

**表字段结构：**
- `mallId` (主键，对应消息中的seller_id)
- `accessToken` (访问令牌)
- `appKey` (应用密钥)
- `appSecret` (应用秘钥)
- `authStatus` (授权状态，必须为"SUCCESS"才能正常处理)

## Webhook回调

成功处理后，会向商户webhook地址发送POST请求：

```json
{
    "success": true,
    "accessToken": "access-token-here",
    "appKey": "app-key-here",
    "appSecret": "app-secret-here",
    "authStatus": "SUCCESS",
    "msgContent": {
        "webhook": "https://merchant.example.com/callback",
        "env": "test",
        "seller_id": "seller123",
        "merchantStoreRegion": "US",
        "temuProductType": "local",
        "imgOption": {
            "imgUrl": "https://example.com/image.jpg",
            "temuImgBizType": 1,
            "temuImgSizeMode": 1,
            "temuCateId": "123"
        }
    },
    "temuResultUrl": "https://processed.image.url"
}
```

失败时的回调：

```json
{
    "success": false,
    "error": "错误描述",
    "msgContent": {
        "webhook": "https://merchant.example.com/callback",
        "env": "test",
        "seller_id": "seller123",
        "merchantStoreRegion": "US",
        "temuProductType": "local",
        "imgOption": {
            "imgUrl": "https://example.com/image.jpg",
            "temuImgBizType": 1,
            "temuImgSizeMode": 1,
            "temuCateId": "123"
        }
    }
}
```

## 异常处理机制

程序使用统一的自定义异常类型：

- **BusinessException**: 业务处理相关异常，通过不同的消息内容区分具体错误类型

## Webhook回调结构说明

**成功回调结构：**
- `success`: true
- `accessToken`: 商户访问令牌
- `appKey`: 商户应用密钥
- `appSecret`: 商户应用秘钥
- `authStatus`: 授权状态
- `msgContent`: 原始SQS消息内容

**失败回调结构：**
- `success`: false
- `error`: 错误描述信息
- `msgContent`: 原始SQS消息内容
- `temuApiResponse`: Temu API完整响应（仅当Temu API业务逻辑失败时包含）

常见的错误情况：
- `dynamodb表名{table_name} Key{mallId} 授权数据 查询不存在或者授权失效` - 商户记录不存在、必要字段为空或authStatus不等于"SUCCESS"
- `不支持的环境: {env}` - env字段不在支持的范围内
- `数据库查询异常相关错误` - DynamoDB连接或查询错误
- `图片下载失败相关错误` - 图片URL无法访问或下载超时

## 处理流程

1. **接收SQS消息** → 解析消息内容
2. **查询DynamoDB** → 根据seller_id和env获取商户认证信息
3. **验证商户数据** → 检查必要字段和授权状态
4. **智能处理**：
   - 如果 `temuProductType = "local"` → 调用Temu本地图片上传API
     - 请求参数：`fileUrl`、`scalingType`
     - API类型：`bg.local.goods.image.upload`
     - 根据`merchantStoreRegion`选择不同的endpoint
   - 如果 `temuProductType = "temu_semi"` → 调用Temu半托管图片处理API
     - 自动下载图片并转换为base64格式
     - 请求参数：`image`(base64格式)、`options`、`imageBizType`
     - API类型：`bg.goods.image.upload`
     - 固定endpoint：`https://openapi.kuajingmaihuo.com/openapi/router`
5. **发送webhook通知** → 返回处理结果和原始消息
6. **删除SQS消息** → 处理完成后清理

## 代码架构

### 核心方法

1. **`process_one_sqs_message(dynamodb_client, message_body)`**
   - 纯业务逻辑处理方法
   - 输入：DynamoDB客户端和消息体
   - 输出：处理结果字典
   - 异常：直接抛出MerchantDataError或TemuApiException

2. **`process_message(sqs_client, dynamodb_client, queue_url, message)`**
   - SQS消息处理协调器
   - 调用process_one_sqs_message处理业务逻辑
   - 处理异常并发送webhook通知
   - 成功时删除SQS消息

### 异常处理策略

- **BusinessException**: 业务处理相关异常（商户数据、图片下载、参数验证等）
- **TemuApiException**: Temu API相关异常（HTTP错误、业务逻辑失败）
- **其他异常**: JSON解析错误、字段缺失等

## Temu API调用

程序提供了完整的Temu API调用支持，包括参数加密和签名功能。

### 参数映射

**temuProductType = "local"**:
- `fileUrl` ← `imgOption.imgUrl`
- `scalingType` ← `imgOption.temuImgSizeMode`

**temuProductType = "temu_semi"**:
- `image` ← 下载`imgOption.imgUrl`并转换为base64格式
- `options.cateId` ← `imgOption.temuCateId`
- `options.sizeMode` ← `imgOption.temuImgSizeMode`
- `imageBizType` ← `imgOption.temuImgBizType`
- `options.boost` = false（固定值）
- `options.doIntelligenceCrop` = false（固定值）

**图片URL转换**（海外服务器优化）:
- `cdn.sdspod.com` → `cdncache3.sdspod.com`

### API Endpoint配置

**temuProductType = "local"**（根据merchantStoreRegion）:
- `US`: `https://openapi-b-us.temu.com/openapi/router`
- `EU`: `https://openapi-b-eu.temu.com/openapi/router`
- `GLOBAL`: `https://openapi-b-global.temu.com/openapi/router`
- 其他区域默认使用GLOBAL endpoint

**temuProductType = "temu_semi"**:
- 固定使用：`https://openapi.kuajingmaihuo.com/openapi/router`

### 返回结果处理

**temuProductType = "local"**:
- 直接提取 `result.url`

**temuProductType = "temu_semi"**:
- 优先级：`result.url` > `result.urls[0]` > `result.imageUrl`
- 按优先级顺序查找第一个非空值

### 签名算法

1. **创建通用请求参数**：
   - type: 请求类型
   - dataType: "JSON"
   - version: 版本号
   - timestamp: 当前时间戳（秒）
   - appKey: 应用密钥
   - accessToken: 访问令牌

2. **合并参数**：将业务参数与通用参数合并

3. **参数排序**：按key的ASCII值排序

4. **生成签名**：
   ```
   签名字符串 = appSecret + key1 + value1 + key2 + value2 + ... + appSecret
   sign = MD5(签名字符串).toUpperCase()
   ```

### 使用示例

```python
# 准备请求数据
request_data = {
    "productId": "12345",
    "quantity": 10
}

# 方式1：只生成签名后的请求体
signed_request = prepare_temu_request(
    request_body=request_data,
    request_type="product.get",
    version="1.0",
    app_key="your_app_key",
    access_token="your_access_token",
    app_secret="your_app_secret"
)

# 方式2：直接调用API
response = call_temu_api(
    endpoint="https://api.temu.com/product/get",
    request_body=request_data,
    request_type="product.get",
    version="1.0",
    app_key="your_app_key",
    access_token="your_access_token",
    app_secret="your_app_secret"
)
```

## 运行测试

```bash
python test_message_processor.py
```

## 日志

程序使用结构化日志，输出JSON格式的日志信息，便于日志聚合和分析。

## 错误处理

- 自动重试机制（可配置重试次数和延迟）
- 详细的错误日志记录
- 失败消息的webhook通知
- 优雅的异常处理

## 监控建议

- 监控SQS队列深度
- 监控处理成功率
- 监控webhook响应时间
- 设置DynamoDB读取容量告警

## 部署建议

- 使用Docker容器化部署
- 配置适当的AWS IAM角色
- 设置健康检查
- 配置日志收集和监控
