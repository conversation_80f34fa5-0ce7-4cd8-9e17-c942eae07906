"""
SQS消息处理器
"""
import json
import boto3
import structlog
from typing import List, Optional
from botocore.exceptions import ClientError, BotoCoreError
from tenacity import retry, stop_after_attempt, wait_exponential

from models import SQSMessage
from config import settings

logger = structlog.get_logger()


class SQSHandler:
    """SQS消息处理器"""
    
    def __init__(self):
        """初始化SQS客户端"""
        try:
            self.sqs = boto3.client('sqs', region_name=settings.aws_region)
            self.queue_url = settings.sqs_queue_url
            logger.info("SQS客户端初始化成功", queue_url=self.queue_url)
        except Exception as e:
            logger.error("SQS客户端初始化失败", error=str(e))
            raise
    
    @retry(
        stop=stop_after_attempt(settings.max_retries),
        wait=wait_exponential(multiplier=settings.retry_delay, min=1, max=10)
    )
    def receive_messages(self, max_messages: int = 10) -> List[dict]:
        """
        从SQS队列接收消息
        
        Args:
            max_messages: 最大接收消息数量
            
        Returns:
            消息列表
        """
        try:
            response = self.sqs.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=max_messages,
                WaitTimeSeconds=20,  # 长轮询
                MessageAttributeNames=['All']
            )
            
            messages = response.get('Messages', [])
            logger.info("接收到SQS消息", count=len(messages))
            return messages
            
        except (ClientError, BotoCoreError) as e:
            logger.error("接收SQS消息失败", error=str(e))
            raise
    
    def parse_message(self, message: dict) -> Optional[SQSMessage]:
        """
        解析SQS消息
        
        Args:
            message: SQS原始消息
            
        Returns:
            解析后的消息对象
        """
        try:
            body = json.loads(message['Body'])
            parsed_message = SQSMessage(**body)
            logger.info("消息解析成功", seller_id=parsed_message.seller_id, env=parsed_message.env)
            return parsed_message
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error("消息解析失败", error=str(e), message_body=message.get('Body'))
            return None
    
    @retry(
        stop=stop_after_attempt(settings.max_retries),
        wait=wait_exponential(multiplier=settings.retry_delay, min=1, max=10)
    )
    def delete_message(self, receipt_handle: str) -> bool:
        """
        删除已处理的消息
        
        Args:
            receipt_handle: 消息接收句柄
            
        Returns:
            删除是否成功
        """
        try:
            self.sqs.delete_message(
                QueueUrl=self.queue_url,
                ReceiptHandle=receipt_handle
            )
            logger.info("消息删除成功", receipt_handle=receipt_handle[:20] + "...")
            return True
            
        except (ClientError, BotoCoreError) as e:
            logger.error("消息删除失败", error=str(e), receipt_handle=receipt_handle[:20] + "...")
            return False
