"""
主业务逻辑处理器
"""
import structlog
from typing import List, <PERSON><PERSON>
from datetime import datetime

from models import SQSMessage, TemuProductRecord
from sqs_handler import <PERSON>QSHandler
from dynamodb_handler import <PERSON>DBHandler
from webhook_handler import <PERSON>hookHandler
from config import settings

logger = structlog.get_logger()


class MessageProcessor:
    """消息处理器 - 整合所有组件的主业务逻辑"""
    
    def __init__(self):
        """初始化所有处理器"""
        self.sqs_handler = SQSHandler()
        self.dynamodb_handler = DynamoDBHandler()
        self.webhook_handler = WebhookHandler()
        
        logger.info("消息处理器初始化完成")
    
    def process_single_message(self, raw_message: dict) -> bool:
        """
        处理单个SQS消息
        
        Args:
            raw_message: 原始SQS消息
            
        Returns:
            处理是否成功
        """
        receipt_handle = raw_message.get('ReceiptHandle')
        message_id = raw_message.get('MessageId', 'unknown')
        
        try:
            # 1. 解析消息
            parsed_message = self.sqs_handler.parse_message(raw_message)
            if not parsed_message:
                logger.error("消息解析失败，跳过处理", message_id=message_id)
                return False
            
            logger.info(
                "开始处理消息",
                message_id=message_id,
                seller_id=parsed_message.seller_id,
                env=parsed_message.env
            )
            
            # 2. 查询DynamoDB获取token
            product_record = self.dynamodb_handler.get_product_record(
                parsed_message.seller_id,
                parsed_message.env
            )
            
            if not product_record:
                error_msg = f"未找到商户记录: seller_id={parsed_message.seller_id}, env={parsed_message.env}"
                logger.error(error_msg)
                
                # 发送失败通知
                self.webhook_handler.send_webhook_notification(
                    str(parsed_message.webhook),
                    parsed_message,
                    success=False,
                    error_message=error_msg
                )
                return False
            
            if not product_record.token:
                error_msg = f"商户token为空: seller_id={parsed_message.seller_id}, env={parsed_message.env}"
                logger.error(error_msg)
                
                # 发送失败通知
                self.webhook_handler.send_webhook_notification(
                    str(parsed_message.webhook),
                    parsed_message,
                    product_record,
                    success=False,
                    error_message=error_msg
                )
                return False
            
            # 3. 发送成功通知到webhook
            webhook_response = self.webhook_handler.send_webhook_notification(
                str(parsed_message.webhook),
                parsed_message,
                product_record,
                success=True
            )
            
            if not webhook_response.success:
                logger.error(
                    "Webhook通知发送失败",
                    message_id=message_id,
                    error=webhook_response.message
                )
                return False
            
            # 4. 删除已处理的消息
            if receipt_handle:
                delete_success = self.sqs_handler.delete_message(receipt_handle)
                if not delete_success:
                    logger.warning("消息删除失败，可能会重复处理", message_id=message_id)
            
            logger.info(
                "消息处理完成",
                message_id=message_id,
                seller_id=parsed_message.seller_id,
                env=parsed_message.env
            )
            return True
            
        except Exception as e:
            logger.error(
                "消息处理异常",
                message_id=message_id,
                error=str(e),
                exc_info=True
            )
            
            # 尝试发送错误通知（如果消息解析成功的话）
            try:
                if 'parsed_message' in locals():
                    self.webhook_handler.send_webhook_notification(
                        str(parsed_message.webhook),
                        parsed_message,
                        success=False,
                        error_message=f"处理异常: {str(e)}"
                    )
            except Exception as webhook_error:
                logger.error("发送错误通知失败", error=str(webhook_error))
            
            return False
    
    def process_single_message_from_queue(self) -> Tuple[int, int]:
        """
        从队列中处理单个消息

        Returns:
            (成功处理数量, 总消息数量)
        """
        try:
            # 每次只接收一个消息
            messages = self.sqs_handler.receive_messages(max_messages=1)

            if not messages:
                logger.debug("没有接收到消息")
                return 0, 0

            # 只处理第一个消息
            message = messages[0]
            logger.info("开始处理单个消息", message_id=message.get('MessageId', 'unknown'))

            if self.process_single_message(message):
                logger.info("消息处理成功")
                return 1, 1
            else:
                logger.warning("消息处理失败")
                return 0, 1

        except Exception as e:
            logger.error("处理消息异常", error=str(e), exc_info=True)
            return 0, 0

    def process_messages_batch(self, max_messages: int = 10) -> Tuple[int, int]:
        """
        批量处理消息（保留兼容性，但建议使用单个消息处理）

        Args:
            max_messages: 最大处理消息数量

        Returns:
            (成功处理数量, 总消息数量)
        """
        try:
            # 接收消息
            messages = self.sqs_handler.receive_messages(max_messages)

            if not messages:
                logger.info("没有接收到消息")
                return 0, 0

            success_count = 0
            total_count = len(messages)

            logger.info("开始批量处理消息", total_count=total_count)

            # 逐个处理消息
            for message in messages:
                if self.process_single_message(message):
                    success_count += 1

            logger.info(
                "批量处理完成",
                success_count=success_count,
                total_count=total_count,
                failure_count=total_count - success_count
            )

            return success_count, total_count

        except Exception as e:
            logger.error("批量处理消息异常", error=str(e), exc_info=True)
            return 0, 0
    
    def run_continuous(self, poll_interval: int = 30):
        """
        持续运行消息处理器（每次处理一个消息）

        Args:
            poll_interval: 轮询间隔（秒）
        """
        logger.info("启动持续消息处理（单消息模式）", poll_interval=poll_interval)

        try:
            import time
            while True:
                try:
                    # 每次只处理一个消息
                    success_count, total_count = self.process_single_message_from_queue()

                    if total_count == 0:
                        logger.debug("等待新消息...")
                        time.sleep(poll_interval)
                    else:
                        # 处理完一个消息后，短暂休息再继续
                        logger.info("消息处理完成，继续监听下一个消息")
                        time.sleep(1)

                except KeyboardInterrupt:
                    logger.info("接收到停止信号，正在关闭...")
                    break
                except Exception as e:
                    logger.error("处理循环异常", error=str(e))
                    time.sleep(poll_interval)

        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            self.webhook_handler.close()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error("资源清理失败", error=str(e))
