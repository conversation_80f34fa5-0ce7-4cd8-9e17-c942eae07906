#!/usr/bin/env python3
"""
Temu接口签名工具
"""
import json
import hashlib
import time
from typing import Dict, Any, Optional
from collections import OrderedDict


class CommonReq:
    """通用请求参数"""
    
    def __init__(self):
        self.type: Optional[str] = None
        self.data_type: str = "JSON"
        self.version: Optional[str] = None
        self.timestamp: int = 0
        self.app_key: Optional[str] = None
        self.access_token: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "type": self.type,
            "dataType": self.data_type,
            "version": self.version,
            "timestamp": self.timestamp,
            "appKey": self.app_key,
            "accessToken": self.access_token
        }


class TemuSignUtil:
    """Temu签名工具类"""
    
    @staticmethod
    def request_body_sign(request_body: Optional[Dict[str, Any]], 
                         common_req: CommonReq, 
                         app_secret: str) -> Dict[str, Any]:
        """
        对请求体进行签名
        
        Args:
            request_body: 请求体数据
            common_req: 通用请求参数
            app_secret: 应用秘钥
            
        Returns:
            签名后的请求体
        """
        if request_body is None:
            request_body = {}
        
        # 复制请求体，避免修改原始数据
        signed_body = request_body.copy()
        
        # 将通用请求参数合并到请求体中
        base_req_dict = common_req.to_dict()
        signed_body.update(base_req_dict)
        
        # 按key的ASCII值排序（Python的sorted默认就是按ASCII排序）
        sorted_items = sorted(signed_body.items())
        
        # 拼接字符串
        concatenated_string = app_secret
        for key, value in sorted_items:
            if value is None:
                continue
            concatenated_string += str(key) + str(value)
        
        concatenated_string += app_secret
        
        # 计算MD5签名并转为大写
        sign = hashlib.md5(concatenated_string.encode('utf-8')).hexdigest().upper()
        signed_body["sign"] = sign
        
        return signed_body
    
    @staticmethod
    def create_common_req(request_type: str, 
                         version: str, 
                         app_key: str, 
                         access_token: str) -> CommonReq:
        """
        创建通用请求参数
        
        Args:
            request_type: 请求类型
            version: 版本号
            app_key: 应用密钥
            access_token: 访问令牌
            
        Returns:
            通用请求参数对象
        """
        common_req = CommonReq()
        common_req.type = request_type
        common_req.data_type = "JSON"
        common_req.version = version
        common_req.timestamp = int(time.time())  # 当前时间戳（秒）
        common_req.app_key = app_key
        common_req.access_token = access_token
        
        return common_req


def prepare_temu_request(request_body: Optional[Dict[str, Any]],
                        request_type: str,
                        version: str,
                        app_key: str,
                        access_token: str,
                        app_secret: str) -> Dict[str, Any]:
    """
    准备Temu API请求（完整流程）
    
    Args:
        request_body: 请求体数据
        request_type: 请求类型
        version: 版本号
        app_key: 应用密钥
        access_token: 访问令牌
        app_secret: 应用秘钥
        
    Returns:
        签名后的完整请求体
    """
    # 创建通用请求参数
    common_req = TemuSignUtil.create_common_req(
        request_type=request_type,
        version=version,
        app_key=app_key,
        access_token=access_token
    )
    
    # 对请求体进行签名
    signed_request = TemuSignUtil.request_body_sign(
        request_body=request_body,
        common_req=common_req,
        app_secret=app_secret
    )
    
    return signed_request


# 使用示例
if __name__ == "__main__":
    # 示例用法
    request_data = {
        "productId": "12345",
        "quantity": 10
    }
    
    signed_request = prepare_temu_request(
        request_body=request_data,
        request_type="product.get",
        version="1.0",
        app_key="your_app_key",
        access_token="your_access_token",
        app_secret="your_app_secret"
    )
    
    print("签名后的请求:")
    print(json.dumps(signed_request, indent=2, ensure_ascii=False))
