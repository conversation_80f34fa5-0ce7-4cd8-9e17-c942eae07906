"""
日志配置模块
"""
import sys
import structlog
from datetime import datetime
from config import settings


def configure_logging():
    """配置结构化日志"""
    
    # 配置structlog
    structlog.configure(
        processors=[
            # 添加时间戳
            structlog.processors.TimeStamper(fmt="ISO"),
            # 添加日志级别
            structlog.stdlib.add_log_level,
            # 添加调用者信息
            structlog.processors.CallsiteParameterAdder(
                parameters=[structlog.processors.CallsiteParameter.FILENAME,
                           structlog.processors.CallsiteParameter.LINENO]
            ),
            # JSON格式化
            structlog.processors.JSONRenderer()
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # 配置Python标准库日志
    import logging
    
    # 设置日志级别
    log_level = getattr(logging, settings.log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=log_level,
        format="%(message)s",
        stream=sys.stdout
    )
    
    # 设置boto3日志级别（避免过多AWS SDK日志）
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def get_logger(name: str = None):
    """获取结构化日志器"""
    return structlog.get_logger(name)
