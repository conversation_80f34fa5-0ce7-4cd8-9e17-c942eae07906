"""
DynamoDB数据访问层
"""
import boto3
import structlog
from typing import Optional
from botocore.exceptions import ClientError, BotoCoreError
from tenacity import retry, stop_after_attempt, wait_exponential

from models import TemuProductRecord
from config import settings

logger = structlog.get_logger()


class DynamoDBHandler:
    """DynamoDB数据访问处理器"""
    
    def __init__(self):
        """初始化DynamoDB客户端"""
        try:
            self.dynamodb = boto3.resource('dynamodb', region_name=settings.aws_region)
            logger.info("DynamoDB客户端初始化成功")
        except Exception as e:
            logger.error("DynamoDB客户端初始化失败", error=str(e))
            raise
    
    def _get_table_name(self, env: str) -> str:
        """
        根据环境获取表名
        
        Args:
            env: 环境名称
            
        Returns:
            完整的表名
        """
        return f"{settings.dynamodb_table_prefix}_{env}"
    
    @retry(
        stop=stop_after_attempt(settings.max_retries),
        wait=wait_exponential(multiplier=settings.retry_delay, min=1, max=10)
    )
    def get_product_record(self, seller_id: str, env: str) -> Optional[TemuProductRecord]:
        """
        根据seller_id和env查询商户产品记录
        
        Args:
            seller_id: 商户ID
            env: 环境
            
        Returns:
            商户产品记录，如果不存在则返回None
        """
        table_name = self._get_table_name(env)
        
        try:
            table = self.dynamodb.Table(table_name)
            
            # 假设seller_id是主键，如果是复合主键需要调整
            response = table.get_item(
                Key={
                    'seller_id': seller_id
                }
            )
            
            if 'Item' in response:
                item = response['Item']
                record = TemuProductRecord(
                    seller_id=item['seller_id'],
                    env=env,
                    token=item.get('token', '')
                )
                logger.info("查询商户记录成功", seller_id=seller_id, env=env)
                return record
            else:
                logger.warning("商户记录不存在", seller_id=seller_id, env=env, table_name=table_name)
                return None
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ResourceNotFoundException':
                logger.error("DynamoDB表不存在", table_name=table_name, seller_id=seller_id, env=env)
            else:
                logger.error("查询DynamoDB失败", error=str(e), seller_id=seller_id, env=env)
            return None
            
        except BotoCoreError as e:
            logger.error("DynamoDB连接错误", error=str(e), seller_id=seller_id, env=env)
            return None
    
    @retry(
        stop=stop_after_attempt(settings.max_retries),
        wait=wait_exponential(multiplier=settings.retry_delay, min=1, max=10)
    )
    def update_product_record(self, seller_id: str, env: str, **kwargs) -> bool:
        """
        更新商户产品记录
        
        Args:
            seller_id: 商户ID
            env: 环境
            **kwargs: 要更新的字段
            
        Returns:
            更新是否成功
        """
        table_name = self._get_table_name(env)
        
        try:
            table = self.dynamodb.Table(table_name)
            
            # 构建更新表达式
            update_expression = "SET "
            expression_attribute_values = {}
            
            for key, value in kwargs.items():
                update_expression += f"{key} = :{key}, "
                expression_attribute_values[f":{key}"] = value
            
            # 移除最后的逗号和空格
            update_expression = update_expression.rstrip(", ")
            
            table.update_item(
                Key={'seller_id': seller_id},
                UpdateExpression=update_expression,
                ExpressionAttributeValues=expression_attribute_values
            )
            
            logger.info("更新商户记录成功", seller_id=seller_id, env=env)
            return True
            
        except (ClientError, BotoCoreError) as e:
            logger.error("更新DynamoDB记录失败", error=str(e), seller_id=seller_id, env=env)
            return False
